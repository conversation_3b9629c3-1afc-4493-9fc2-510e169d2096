*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
    color: #fff;
    font-size: 16px;
    font-family: 'Orbitron', sans-serif;
}

#body {
    background-color: #000;
    width: 100vw;
    padding: 0 10em;
    display: flex;
    flex-direction: column;
}

#header,
#footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em;
}

#header a {
    text-decoration: none;
    color: inherit;
    margin: 0 1rem;
}

.logo {
    width: 5%;
}

.container {
    min-height: 100vh;
    padding: 1em;
}

#select-season {
    background-color: #181818;
    color: #fff;
    border: none;
    padding: 0.5em;
    border-radius: 0.5em;
    font-size: 1rem;
    font-family: 'Orbitron', sans-serif;
    margin-top: 2rem;
}

table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 15px;
    background-color: #181818;
    margin-top: 1rem;
}

table th,
table td {
    padding: 1.25em;
    text-align: left;
}

.quote {
    font-style: italic;
    font-weight: bold;
}

@media screen and (max-width: 768px) {
  #body {
    padding: 0 1em;
  }

  .logo {
    width: 10%;
  }

  #header,
  #footer {
    padding: 0.5em 1em;
    gap: 1em;
  }

  #header a {
    margin: 0.5rem;
  }

  #select-season {
    margin-top: 1rem;
    width: 100%;
  }

  .container {
    padding: 1em;
    min-height: 50vh;
    overflow: hidden;
  }

  table {
    overflow: hidden;
  }

  table th,
  table td {
    padding: 0.75em;
    font-size: 0.85rem;
  }
}






































































































/* *,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
    font-size: 32px;
    font-family: 'Orbitron', sans-serif;
    color: #fff;
}

#body {
    display: flex;
    flex-direction: column;
    background-color: #000;
    width: 100%;
    padding: 0 10em;
}

.logo {
    width: 10%;
}

#header,
#footer {
    display: flex;
    gap: 1%;
    align-items: center;
    justify-content: space-between;
    background-color: #000;
    padding: 1em;
}

#nav-bar a{
    text-decoration: none;
    color: inherit;
    margin-left: 1rem;
}

#select-season {
    background-color: #181818;
    color: #fff;
    border: none;
    padding: 0.5em;
    border-radius: 0.5em;
    font-size: 1rem;
    font-family: 'Orbitron', sans-serif;
    margin-top: 2rem;
}

.container {
    min-height: 100vh;
    padding: 3em;
}

table {
    background-color: #181818;
    width: 100%;
    margin-top: 1rem;
    border-collapse: collapse;
    border-radius: 25px;
}

table th,
table td {
    padding: 1em;
    text-align: left;
} */
