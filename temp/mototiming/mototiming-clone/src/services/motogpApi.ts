const BASE_URL = 'https://api.motogp.pulselive.com/motogp/v1';

export const getSeason = async (year: number) => {
  try {
    const response = await fetch(`${BASE_URL}/results/seasons/${year}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching season ${year}:`, error);
    throw error;
  }
};

export const getEvent = async (seasonId: string, eventId: string) => {
  try {
    const response = await fetch(`${BASE_URL}/results/seasons/${seasonId}/events/${eventId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching event ${eventId}:`, error);
    throw error;
  }
};

export const getSession = async (seasonId: string, eventId: string, sessionId: string) => {
  try {
    const response = await fetch(`${BASE_URL}/results/seasons/${seasonId}/events/${eventId}/sessions/${sessionId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching session ${sessionId}:`, error);
    throw error;
  }
};