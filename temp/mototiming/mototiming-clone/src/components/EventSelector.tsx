import React, { useState, useEffect } from 'react';
import { getSeason } from '../services/motogpApi';

const EventSelector = ({ seasonId, onEventSelect }: { seasonId: string, onEventSelect: (eventId: string) => void }) => {
  const [events, setEvents] = useState<any[]>([]);

  useEffect(() => {
    const fetchEvents = async () => {
      if (seasonId) {
        try {
          const seasonData = await getSeason(parseInt(seasonId));
          setEvents(seasonData.events);
        } catch (error) {
          // Handle error
        }
      }
    };

    fetchEvents();
  }, [seasonId]);

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onEventSelect(event.target.value);
  };

  return (
    <select onChange={handleChange}>
      {events.map(event => (
        <option key={event.id} value={event.id}>
          {event.name}
        </option>
      ))}
    </select>
  );
};

export default EventSelector;
