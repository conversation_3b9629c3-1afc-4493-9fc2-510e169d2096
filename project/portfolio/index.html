<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Personal Portfolio</title>
        <link rel="stylesheet" href="styles.css">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    </head>
    <body>
        <main>
            <aside class="sidebar" data-sidebar>
                <div class="sidebar-info">
                    <div class="info-content">
                    <h1 class="name" title="Akil S">Akil S</h1>
                    <p class="title">Web developer</p>
                    </div>
                    <button class="info_more-btn" data-sidebar-btn>
                    <span>Contact me:</span>
                    </button>
                </div>
                <div class="sidebar-info_more">
                    <div class="separator"></div>
                    <ul class="contacts-list">
                        <li class="contact-item">
                            <div class="icon-box">
                                <ion-icon name="mail-outline"></ion-icon>
                            </div>
                            <div class="contact-info">
                                <p class="contact-title">Email</p>
                                <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                            </div>
                        </li>
                        <li class="contact-item">
                            <div class="icon-box">
                                <ion-icon name="phone-portrait-outline"></ion-icon>
                            </div>
                            <div class="contact-info">
                                <p class="contact-title">Phone</p>
                                <a href="tel:+918778631458" class="contact-link">+91 ************</a>
                            </div>
                        </li>
                        <li class="contact-item">
                                <div class="icon-box">
                            <ion-icon name="location-outline"></ion-icon>
                            </div>
                                <div class="contact-info">
                                <p class="contact-title">Location</p>
                            <address>Chennai, Tamil Nadu, India</address>
                            </div>
                        </li>
                    </ul>
                <div class="separator"></div>
                    <ul class="social-list">
                        <li class="social-item">
                            <a href="https://github.com/akil4" class="social-link">
                                <ion-icon name="logo-github"></ion-icon>
                            </a>
                        </li>
                        <li class="social-item">
                            <a href="https://www.linkedin.com/in/akil4" class="social-link">
                                <ion-icon name="logo-linkedin"></ion-icon>
                            </a>
                        </li>
                    </ul>
                </div>
            </aside>
            <div class="main-content">
            <nav class="navbar">
                <ul class="navbar-list">
                    <li class="navbar-item">
                        <button class="navbar-link  active" data-nav-link>About</button>
                    </li>
                    <li class="navbar-item">
                        <button class="navbar-link" data-nav-link>Resume</button>
                    </li>
                    <li class="navbar-item">
                        <button class="navbar-link" data-nav-link>Projects</button>
                     </li>
                </ul>
            </nav>
            <article class="about  active" data-page="about">
                <header>
                    <h2 class="h2 article-title">About me</h2>
                </header>
                <section class="about-text">
                    <p>
                        Hey there! I'm Akil, and I'm an aspiring engineer right here from Chennai. What really gets me going is the thrill of problem-solving with code – I love taking a big, complex idea and breaking it down into a clean, functional solution. I'm super passionate about diving into emerging tech and honestly, just building things that make a real difference in the world. I'm on the lookout for a dynamic team where I can jump in, collaborate, learn a ton, and contribute to some truly impactful software development.
                    </p>
                </section>
                <section class="service">
                    <h3 class="h3 service-title">Tech Stack</h3>
                    <ul class="service-list">
                        <li class="service-item">
                            <div class="service-icon-box">
                                <ion-icon name="logo-html5"></ion-icon>
                            </div>
                            <div class="service-content-box">
                                <h4 class="h4 service-item-title">HTML</h4>
                                <p class="service-item-text">
                                Proficient in semantic HTML5 for structured content, accessibility, and modern web standards.
                                </p>
                            </div>
                        </li>
                        <li class="service-item">
                            <div class="service-icon-box">
                                <ion-icon name="logo-css3"></ion-icon>
                            </div>
                            <div class="service-content-box">
                                <h4 class="h4 service-item-title">CSS</h4>
                                <p class="service-item-text">
                                Proficient in modern CSS, including Flexbox, Grid, and media queries for responsive and visually appealing designs.
                                </p>
                            </div>
                        </li>
                        <li class="service-item">
                            <div class="service-icon-box">
                                <ion-icon name="logo-javascript"></ion-icon>
                            </div>
                            <div class="service-content-box">
                                <h4 class="h4 service-item-title">JavaScript</h4>
                                <p class="service-item-text">
                                Strong in vanilla JavaScript for interactive web experiences.
                                </p>
                            </div>
                        </li>
                        <li class="service-item">
                            <div class="service-icon-box">
                                <ion-icon name="git-merge-outline"></ion-icon>
                            </div>
                            <div class="service-content-box">
                                <h4 class="h4 service-item-title">Git</h4>
                                <p class="service-item-text">
                                Expert in version control, utilizing branching, merging, pull requests, and conflict resolution for collaborative development on GitHub.
                                </p>
                            </div>
                        </li>
                    </ul>
                </section>

                <!--
                - testimonials modal
                -->
                <div class="modal-container" data-modal-container>
                <div class="overlay" data-overlay></div>
                <section class="testimonials-modal">
                    <button class="modal-close-btn" data-modal-close-btn>
                    <ion-icon name="close-outline"></ion-icon>
                    </button>
                    <div class="modal-img-wrapper">
                    <figure class="modal-avatar-box">
                        <img src="./assets/images/avatar-1.png" alt="Daniel lewis" width="80" data-modal-img>
                    </figure>
                    <img src="./assets/images/icon-quote.svg" alt="quote icon">
                    </div>
                    <div class="modal-content">
                    <h4 class="h3 modal-title" data-modal-title>Daniel lewis</h4>
                    <time datetime="2021-06-14">14 June, 2021</time>
                    <div data-modal-text>
                        <p>
                        Richard was hired to create a corporate identity. We were very pleased with the work done. She has a
                        lot of experience
                        and is very concerned about the needs of client. Lorem ipsum dolor sit amet, ullamcous cididt
                        consectetur adipiscing
                        elit, seds do et eiusmod tempor incididunt ut laborels dolore magnarels alia.
                        </p>
                    </div>
                    </div>
                </section>
                </div>

            </article>
            <article class="resume" data-page="resume">
                <header>
                    <h2 class="h2 article-title">Resume</h2>
                </header>
                <section class="timeline">
                    <div class="title-wrapper">
                        <div class="icon-box">
                        <ion-icon name="book-outline"></ion-icon>
                        </div>
                        <h3 class="h3">Education</h3>
                    </div>
                    <ol class="timeline-list">
                        <li class="timeline-item">
                            <h4 class="h4 timeline-item-title">Panimalar Engineering College Chennai City Campus</h4>
                            <span>2022 - 2026</span>
                            <p class="timeline-text">
                                B.E. Computer Science and Engineering - 8.01 CGPA
                            </p>
                        </li>
                        <li class="timeline-item">
                            <h4 class="h4 timeline-item-title">Daniel Thomas Matriculation  Higher Secondary School</h4>
                            <span>2021 - 2022</span>
                            <p class="timeline-text">
                                HSC - 76.5%
                            </p>
                        </li>
                        <li class="timeline-item">
                            <h4 class="h4 timeline-item-title">Daniel Thomas Matriculation Higher Secondary School</h4>
                            <span>2019 - 2020</span>
                            <p class="timeline-text">
                                SSLC - 77%
                            </p>
                        </li>
                    </ol>
                </section>
                <section class="skill">
                    <h3 class="h3 skills-title">My skills</h3>
                    <ul class="skills-list content-card">
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">HTML</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">CSS</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">JavaScript</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">Git</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">GitHub</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">Java</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">Python</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">C / C++</h5>
                            </div>
                        </li>
                        <li class="skills-item">
                            <div class="title-wrapper">
                                <h5 class="h5">MySQL</h5>
                            </div>
                        </li>
                    </ul>
                </section>
                <section class="certifications">
                    <h3 class="h3 skills-title">Certifications</h3>
                    <ul class="certifications-list content-card">
                        <li class="certifications-item">
                            <div class="title-wrapper">
                                <h5 class="h5">TCSion's Applied Cloud Computing</h5>
                            </div>
                        </li>
                        <li class="certifications-item">
                            <div class="title-wrapper">
                                <h5 class="h5">freeCodeCamp's Responsive Web Design</h5>
                            </div>
                        </li>
                    </ul>
                </section>
            </article>
            <article class="projects  active" data-page="projects">
                <header>
                    <h2 class="h2 projects-title">Projects</h2>
                </header>
                <section class="service project-link">
                    <ul class="service-list">
                        <a href="https://motolive.vercel.app" target="_blank">
                            <li class="service-item">
                                <div class="service-icon-box">
                                    
                                </div>
                                <div class="service-content-box">
                                    <h4 class="h4 service-item-title">MotoLive</h4>
                                    <p class="service-item-text">
                                    Built a responsive frontend that displays live race sessions, results, and standings using clean HTML, CSS, and vanilla JavaScript. Used Node.js to create scripts that fetch racing data from a community-maintained API and update static .json files. Deployed the static site on Vercel for fast global delivery and automated continuous deployment on code or data updates. Automated the data fetching and deployment workflow using GitHub Actions to keep race data up to date without manual effort.
                                    </p>
                                </div>
                            </li>
                        </a>
                    </ul>
                </section>

                <!--
                - testimonials modal
                -->
                <div class="modal-container" data-modal-container>
                <div class="overlay" data-overlay></div>
                <section class="testimonials-modal">
                    <button class="modal-close-btn" data-modal-close-btn>
                    <ion-icon name="close-outline"></ion-icon>
                    </button>
                    <div class="modal-img-wrapper">
                    <figure class="modal-avatar-box">
                        <img src="./assets/images/avatar-1.png" alt="Daniel lewis" width="80" data-modal-img>
                    </figure>
                    <img src="./assets/images/icon-quote.svg" alt="quote icon">
                    </div>
                    <div class="modal-content">
                    <h4 class="h3 modal-title" data-modal-title>Daniel lewis</h4>
                    <time datetime="2021-06-14">14 June, 2021</time>
                    <div data-modal-text>
                        <p>
                        Richard was hired to create a corporate identity. We were very pleased with the work done. She has a
                        lot of experience
                        and is very concerned about the needs of client. Lorem ipsum dolor sit amet, ullamcous cididt
                        consectetur adipiscing
                        elit, seds do et eiusmod tempor incididunt ut laborels dolore magnarels alia.
                        </p>
                    </div>
                    </div>
                </section>
                </div>

            </article>
            <!--
                - #PORTFOLIO
            -->
            <article class="portfolio" data-page="portfolio">
                <header>
                    <h2 class="h2 article-title">Portfolio</h2>
                </header>
                <section class="projects">
                    <ul class="filter-list">
                        <li class="filter-item">
                            <button class="active" data-filter-btn>All</button>
                        </li>
                        <li class="filter-item">
                            <button data-filter-btn>Web design</button>
                        </li>
                        <li class="filter-item">
                            <button data-filter-btn>Applications</button>
                        </li>
                        <li class="filter-item">
                            <button data-filter-btn>Web development</button>
                        </li>
                    </ul>
                    <div class="filter-select-box">
                        <button class="filter-select" data-select>
                            <div class="select-value" data-selecct-value>Select category</div>
                            <div class="select-icon">
                                <ion-icon name="chevron-down"></ion-icon>
                            </div>
                        </button>
                        <ul class="select-list">
                            <li class="select-item">
                                <button data-select-item>All</button>
                            </li>
                            <li class="select-item">
                                <button data-select-item>Web design</button>
                            </li>
                            <li class="select-item">
                                <button data-select-item>Applications</button>
                            </li>
                            <li class="select-item">
                                <button data-select-item>Web development</button>
                            </li>
                        </ul>
                    </div>
                    <ul class="project-list">
                        <li class="project-item  active" data-filter-item data-category="web development">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-1.jpg" alt="finance" loading="lazy">
                                </figure>
                                <h3 class="project-title">Finance</h3>
                                <p class="project-category">Web development</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web development">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-2.png" alt="orizon" loading="lazy">
                                </figure>
                                <h3 class="project-title">Orizon</h3>
                                <p class="project-category">Web development</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web design">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-3.jpg" alt="fundo" loading="lazy">
                                </figure>
                                <h3 class="project-title">Fundo</h3>
                                <p class="project-category">Web design</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="applications">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-4.png" alt="brawlhalla" loading="lazy">
                                </figure>
                                <h3 class="project-title">Brawlhalla</h3>
                                <p class="project-category">Applications</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web design">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-5.png" alt="dsm." loading="lazy">
                                </figure>
                                <h3 class="project-title">DSM.</h3>
                                <p class="project-category">Web design</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web design">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-6.png" alt="metaspark" loading="lazy">
                                </figure>
                                <h3 class="project-title">MetaSpark</h3>
                                <p class="project-category">Web design</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web development">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-7.png" alt="summary" loading="lazy">
                                </figure>
                                <h3 class="project-title">Summary</h3>
                                <p class="project-category">Web development</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="applications">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-8.jpg" alt="task manager" loading="lazy">
                                </figure>
                                <h3 class="project-title">Task Manager</h3>
                                <p class="project-category">Applications</p>
                            </a>
                        </li>
                        <li class="project-item  active" data-filter-item data-category="web development">
                            <a href="#">
                                <figure class="project-img">
                                    <div class="project-item-icon-box">
                                        <ion-icon name="eye-outline"></ion-icon>
                                    </div>
                                    <img src="./assets/images/project-9.png" alt="arrival" loading="lazy">
                                </figure>
                                <h3 class="project-title">Arrival</h3>
                                <p class="project-category">Web development</p>
                            </a>
                        </li>
                    </ul>
                </section>
            </article>
            </div>
        </main>
        <script src="main.js"></script>
        <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
        <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    </body>
</html>