document.addEventListener('DOMContentLoaded', () => {
    const newTaskInput = document.getElementById('new-task-input');
    const addTaskButton = document.getElementById('add-task-button');
    const taskContainer = document.querySelector('.task-container');
    const noTasksMessage = document.getElementById('no-tasks-message');

    // Function to save tasks to localStorage
    const saveTasks = (tasks) => {
        localStorage.setItem('tasks', JSON.stringify(tasks));
        updateNoTasksMessage();
    };

    // Function to load tasks from localStorage
    const loadTasks = () => {
        const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        tasks.forEach(task => createTaskElement(task.text, task.completed, task.id));
        updateNoTasksMessage();
    };

    // Function to update the "No tasks yet!" message visibility
    const updateNoTasksMessage = () => {
        if (taskContainer.children.length === 1 && taskContainer.children[0].id === 'no-tasks-message') {
            noTasksMessage.style.display = 'block';
        } else if (taskContainer.children.length === 0) {
            noTasksMessage.style.display = 'block';
        } else {
            noTasksMessage.style.display = 'none';
        }
    };

    // Function to create a new task element
    const createTaskElement = (taskText, isCompleted = false, taskId = Date.now()) => {
        const taskItem = document.createElement('div');
        taskItem.classList.add('task-item');
        taskItem.dataset.id = taskId; // Store ID for easy reference
        if (isCompleted) {
            taskItem.classList.add('completed');
        }

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.classList.add('task-checkbox');
        checkbox.checked = isCompleted;
        checkbox.id = `task-${taskId}`; // Unique ID for checkbox

        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.classList.add('task-text');
        textInput.value = taskText;
        textInput.id = `task-${taskId}-text`; // Unique ID for text input
        textInput.setAttribute('readonly', 'true'); // Make it read-only by default

        const deleteButton = document.createElement('button');
        deleteButton.classList.add('delete-button');
        deleteButton.innerHTML = '<i class="fas fa-trash"></i>'; // Font Awesome trash icon

        // Event Listeners for the new task item
        checkbox.addEventListener('change', () => {
            taskItem.classList.toggle('completed', checkbox.checked);
            updateTaskInLocalStorage(taskId, 'completed', checkbox.checked);
        });

        textInput.addEventListener('dblclick', () => {
            textInput.removeAttribute('readonly');
            textInput.focus();
        });

        textInput.addEventListener('blur', () => {
            textInput.setAttribute('readonly', 'true');
            updateTaskInLocalStorage(taskId, 'text', textInput.value);
        });

        // Allow 'Enter' key to save changes after editing
        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                textInput.blur(); // Trigger blur to save
            }
        });

        deleteButton.addEventListener('click', () => {
            taskItem.remove();
            removeTaskFromLocalStorage(taskId);
        });

        taskItem.appendChild(checkbox);
        taskItem.appendChild(textInput);
        taskItem.appendChild(deleteButton);
        taskContainer.appendChild(taskItem);
        updateNoTasksMessage();
    };

    // Function to add a new task
    const addTask = () => {
        const taskText = newTaskInput.value.trim();
        if (taskText !== '') {
            const taskId = Date.now(); // Unique ID for the task
            createTaskElement(taskText, false, taskId);
            addTaskToLocalStorage(taskText, false, taskId);
            newTaskInput.value = ''; // Clear input field
        }
    };

    // Functions to interact with localStorage
    const addTaskToLocalStorage = (text, completed, id) => {
        const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        tasks.push({ id, text, completed });
        saveTasks(tasks);
    };

    const updateTaskInLocalStorage = (id, key, value) => {
        let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        tasks = tasks.map(task => {
            if (task.id === id) {
                return { ...task, [key]: value };
            }
            return task;
        });
        saveTasks(tasks);
    };

    const removeTaskFromLocalStorage = (id) => {
        let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        tasks = tasks.filter(task => task.id !== id);
        saveTasks(tasks);
    };

    // Event listeners
    addTaskButton.addEventListener('click', addTask);
    newTaskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addTask();
        }
    });

    // Initial load of tasks when the page loads
    loadTasks();
});