import React, { useState, useEffect } from 'react';
import { getSeason } from '../services/motogpApi';

const Standings = () => {
  const [standings, setStandings] = useState<any[]>([]);

  useEffect(() => {
    const fetchStandings = async () => {
      try {
        // I can't find the right API to get the standings from.
      } catch (error) {
        // Handle error
      }
    };

    fetchStandings();
  }, []);

  return (
    <div>
      <h2>Standings</h2>
      <p>Standings data is not available at the moment.</p>
    </div>
  );
};

export default Standings;