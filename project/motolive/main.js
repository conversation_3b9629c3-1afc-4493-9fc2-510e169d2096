async function loadSeasons(data/seasons.json, ) {
    const response = await fetch('data/seasons.json');
    const jsonData = await response.json();
    const select = document.getElementById('select-season');
    
    jsonData.forEach(season => {
        const option = document.createElement('option');
        option.value = season.id;
        option.text = season.name;
        select.appendChild(option);
    });
}