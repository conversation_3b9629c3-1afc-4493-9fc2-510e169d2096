import React, { useState, useEffect } from 'react';
import { getSession, getEvent } from '../services/motogpApi';
import SessionResults from './SessionResults';
import SeasonSelector from './SeasonSelector';
import EventSelector from './EventSelector';

const Dashboard = () => {
  const [seasonId, setSeasonId] = useState('2024');
  const [eventId, setEventId] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [sessionData, setSessionData] = useState(null);
  const [eventData, setEventData] = useState(null);

  useEffect(() => {
    const fetchEventData = async () => {
      if (eventId) {
        try {
          const data = await getEvent(seasonId, eventId);
          setEventData(data);
        } catch (error) {
          // Handle error
        }
      }
    };

    fetchEventData();
  }, [seasonId, eventId]);

  useEffect(() => {
    const fetchSessionData = async () => {
      if (sessionId) {
        try {
          const data = await getSession(seasonId, eventId, sessionId);
          setSessionData(data);
        } catch (error) {
          // Handle error
        }
      }
    };

    fetchSessionData();
  }, [seasonId, eventId, sessionId]);

  return (
    <div>
      <h2>Live Timing</h2>
      <SeasonSelector onSeasonSelect={setSeasonId} />
      {seasonId && <EventSelector seasonId={seasonId} onEventSelect={setEventId} />}
      {eventData && (
        <select onChange={(e) => setSessionId(e.target.value)}>
          {eventData.sessions.map((session: any) => (
            <option key={session.id} value={session.id}>
              {session.name}
            </option>
          ))}
        </select>
      )}
      {sessionData ? <SessionResults data={sessionData} /> : <p>Loading...</p>}
    </div>
  );
};

export default Dashboard;