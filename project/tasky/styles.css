*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
    color: #fff;
    font-size: 16px;
    font-family: '<PERSON>o', sans-serif;
}

body {
    background-color: #000;
    width: 100vw;
    padding: 0 10em;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
}

#h1 {
    padding: 0.5em;
}

.task-container {
    margin: 1rem;
    padding: 1rem;
    background-color: #181818;
    border-radius: 1rem;
    min-height: 85vh;
    max-height: 85vh;
    overflow-x: hidden;
    overflow-y: auto;
}

.task-item {
    display: flex;
    gap: 1rem;
    padding: 1em;
    border-bottom: 2px solid #282828;
}

.task-item .task-text {
    min-width: 90%;
}

#footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#footer a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

#footer a:hover {
    font-size: 1.5rem;
    color: #549c54
}