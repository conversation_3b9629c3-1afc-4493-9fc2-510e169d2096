import React from 'react';

const SeasonSelector = ({ onSeasonSelect }: { onSeasonSelect: (seasonId: string) => void }) => {
  const years = [2024, 2023, 2022, 2021, 2020]; // Add more years as needed

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onSeasonSelect(event.target.value);
  };

  return (
    <select onChange={handleChange}>
      {years.map(year => (
        <option key={year} value={year}>
          {year}
        </option>
      ))}
    </select>
  );
};

export default SeasonSelector;
