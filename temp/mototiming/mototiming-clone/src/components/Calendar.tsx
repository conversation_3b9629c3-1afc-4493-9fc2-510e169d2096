import React, { useState, useEffect } from 'react';
import { getSeason } from '../services/motogpApi';

const Calendar = () => {
  const [events, setEvents] = useState<any[]>([]);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const seasonData = await getSeason(2024); // Hardcoded for now
        setEvents(seasonData.events);
      } catch (error) {
        // Handle error
      }
    };

    fetchEvents();
  }, []);

  return (
    <div>
      <h2>Calendar</h2>
      <ul>
        {events.map(event => (
          <li key={event.id}>
            {event.name} - {event.date_start} to {event.date_end}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Calendar;