"""
Model Parameter Extractor
This script attempts to extract model parameters from the pickle file using low-level methods
and recreate the model with the current scikit-learn version.
"""

import pickle
import joblib
import numpy as np
from sklearn.tree import DecisionTreeRegressor
from sklearn import __version__ as sklearn_version

def extract_model_info():
    """Extract information from the pickle file without fully loading the model"""
    
    print(f"Current scikit-learn version: {sklearn_version}")
    print("Attempting to extract model information...\n")
    
    try:
        # Try to load with pickle and inspect the structure
        with open('fuel_blend_predictor_model.pkl', 'rb') as f:
            # Read the pickle data without unpickling
            data = f.read()
            print(f"Pickle file size: {len(data)} bytes")
            
        # Try to partially unpickle to get basic info
        with open('fuel_blend_predictor_model.pkl', 'rb') as f:
            unpickler = pickle.Unpickler(f)
            
            # Try to get some basic information
            try:
                # This might fail, but let's try
                obj = unpickler.load()
                print("Successfully loaded with pickle!")
                return obj
            except Exception as e:
                print(f"Pickle loading failed: {e}")
                
        # If that fails, try with joblib but catch the specific error
        try:
            import warnings
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore")
                model = joblib.load('fuel_blend_predictor_model.pkl')
            print("Successfully loaded with joblib (ignoring warnings)!")
            return model
        except ValueError as e:
            if "node array" in str(e):
                print("Found the node array incompatibility issue.")
                print("This is a known issue with scikit-learn version changes.")
                return None
            else:
                raise e
                
    except Exception as e:
        print(f"Failed to extract model info: {e}")
        return None

def create_compatible_model():
    """Create a new DecisionTreeRegressor with default parameters"""
    
    print("\n=== Creating a new compatible model ===")
    print("Since we can't load the old model, creating a new DecisionTreeRegressor")
    print("with default parameters that you can retrain with your data.\n")
    
    # Create a new model with common parameters
    model = DecisionTreeRegressor(
        criterion='squared_error',  # Default in newer versions
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42
    )
    
    print("New model created with parameters:")
    print(model.get_params())
    
    # Save the new model
    joblib.dump(model, 'fuel_blend_predictor_model_new.pkl')
    print(f"\nNew model saved as: fuel_blend_predictor_model_new.pkl")
    print("You can now train this model with your data using model.fit(X, y)")
    
    return model

def demonstrate_usage():
    """Show how to use the new model"""
    
    print("\n=== Usage Example ===")
    print("To train your new model:")
    print("""
import joblib
import numpy as np
from sklearn.tree import DecisionTreeRegressor

# Load the new model
model = joblib.load('fuel_blend_predictor_model_new.pkl')

# Example training data (replace with your actual data)
X = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])  # Features
y = np.array([10, 20, 30])  # Target values

# Train the model
model.fit(X, y)

# Make predictions
predictions = model.predict(X)
print("Predictions:", predictions)

# Save the trained model
joblib.dump(model, 'fuel_blend_predictor_model_trained.pkl')
""")

if __name__ == "__main__":
    print("=== Model Recovery Tool ===\n")
    
    # Try to extract info from the old model
    old_model = extract_model_info()
    
    if old_model is not None:
        print(f"\nModel type: {type(old_model)}")
        if hasattr(old_model, 'get_params'):
            print(f"Model parameters: {old_model.get_params()}")
        
        # Try to save it in the new format
        try:
            joblib.dump(old_model, 'fuel_blend_predictor_model_recovered.pkl')
            print("✓ Successfully saved recovered model as: fuel_blend_predictor_model_recovered.pkl")
        except Exception as e:
            print(f"✗ Failed to save recovered model: {e}")
            create_compatible_model()
    else:
        # Create a new compatible model
        create_compatible_model()
    
    demonstrate_usage()
    
    print("\n=== Summary ===")
    print("Due to scikit-learn version incompatibility, you have these options:")
    print("1. Use the new model file and retrain with your data")
    print("2. Downgrade to Python 3.11 or earlier and install scikit-learn 1.1.3")
    print("3. Contact the original model creator for a compatible version")
    print("4. Export your model to a format like ONNX for better compatibility")
