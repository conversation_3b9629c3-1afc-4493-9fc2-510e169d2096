// Function to load seasons data from JSON file
async function loadSeasons() {
    const seasonsData = await fetch('data/seasons.json');
    const jsonData = await seasonsData.json();
    const selectSeason = document.getElementById('select-season');
    
    // Check if the 'select-season' element exists
    if (!selectSeason) {
        console.error("Element with ID 'select-season' not found.");
        return;
    }

    // Load seasons data
    jsonData.forEach(season => {
        selectSeason.add(new Option(season.name, season.id));
    });
}

async function loadStandings() {
    const standingsData = await fetch('data/standings.json');
    const jsonData = await standingsData.json();
    const championshipTable = document.getElementById('championship-table');
    
    if (!championshipTable) {
        console.error("Element with ID 'championship-table' not found.");
        return;
    }

    
}

loadSeasons();
loadStandings();