"""
Model Converter Script
This script attempts to load an old model and re-save it in a format compatible with the current scikit-learn version.
"""

import joblib
import pickle
import warnings
from sklearn import __version__ as sklearn_version
from sklearn.tree import DecisionTreeRegressor

def try_load_with_different_methods():
    """Try different methods to load the model"""
    
    print(f"Current scikit-learn version: {sklearn_version}")
    print("Attempting to load model with different methods...\n")
    
    # Method 1: Direct joblib load (will likely fail)
    try:
        print("Method 1: Direct joblib.load()")
        model = joblib.load('fuel_blend_predictor_model.pkl')
        print("✓ Success with direct joblib.load()")
        return model, "joblib"
    except Exception as e:
        print(f"✗ Failed: {e}\n")
    
    # Method 2: Load with warnings suppressed
    try:
        print("Method 2: joblib.load() with warnings suppressed")
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore")
            model = joblib.load('fuel_blend_predictor_model.pkl')
        print("✓ Success with warnings suppressed")
        return model, "joblib_no_warnings"
    except Exception as e:
        print(f"✗ Failed: {e}\n")
    
    # Method 3: Try with pickle directly
    try:
        print("Method 3: Direct pickle.load()")
        with open('fuel_blend_predictor_model.pkl', 'rb') as f:
            model = pickle.load(f)
        print("✓ Success with pickle.load()")
        return model, "pickle"
    except Exception as e:
        print(f"✗ Failed: {e}\n")
    
    return None, None

def save_compatible_model(model, method_used):
    """Save the model in a format compatible with current version"""
    
    if model is None:
        print("No model to save.")
        return
    
    try:
        # Save with current joblib version
        new_filename = f'fuel_blend_predictor_model_v{sklearn_version.replace(".", "_")}.pkl'
        joblib.dump(model, new_filename)
        print(f"✓ Model re-saved as: {new_filename}")
        print(f"Original loading method: {method_used}")
        
        # Test loading the new model
        test_model = joblib.load(new_filename)
        print("✓ New model loads successfully!")
        
        return new_filename
        
    except Exception as e:
        print(f"✗ Failed to save new model: {e}")
        return None

if __name__ == "__main__":
    print("=== Model Compatibility Converter ===\n")
    
    model, method = try_load_with_different_methods()
    
    if model:
        print(f"\nModel Details:")
        print(f"Type: {type(model)}")
        if hasattr(model, 'get_params'):
            print(f"Parameters: {model.get_params()}")
        
        new_file = save_compatible_model(model, method)
        
        if new_file:
            print(f"\n=== SUCCESS ===")
            print(f"Your model has been converted and saved as: {new_file}")
            print("You can now use this file with your current scikit-learn version.")
        
    else:
        print("\n=== FAILED ===")
        print("Could not load the model with any method.")
        print("\nRecommended solutions:")
        print("1. Downgrade scikit-learn: pip install scikit-learn==1.1.3")
        print("2. Re-train your model with the current scikit-learn version")
        print("3. Contact the person who created the model for a compatible version")
