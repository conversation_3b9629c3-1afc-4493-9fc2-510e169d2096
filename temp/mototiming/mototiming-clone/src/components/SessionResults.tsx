import React from 'react';
import './SessionResults.css';

const SessionResults = ({ data }: { data: any }) => {
  if (!data || !data.classification) {
    return <p>No data available.</p>;
  }

  return (
    <table>
      <thead>
        <tr>
          <th>Pos</th>
          <th>Rider</th>
          <th>Team</th>
          <th>Bike</th>
          <th>Time</th>
          <th>Gap</th>
        </tr>
      </thead>
      <tbody>
        {data.classification.map((rider: any) => (
          <tr key={rider.rider.id}>
            <td>{rider.position}</td>
            <td>{rider.rider.full_name}</td>
            <td>{rider.team.name}</td>
            <td>{rider.bike.name}</td>
            <td>{rider.time}</td>
            <td>{rider.gap}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default SessionResults;