<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Data Loader</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin: 20px 0;
        }
        .data-item {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>JSON Data Loader Example</h1>
    
    <div class="container">
        <button onclick="loadJSONFromURL()">Load from URL</button>
        <button onclick="loadJSONFromString()">Load from String</button>
        <button onclick="clearData()">Clear Data</button>
    </div>
    
    <div id="output"></div>

    <script>
        // Function to load JSON data from a URL
        async function loadJSONFromURL(url = 'https://jsonplaceholder.typicode.com/posts?_limit=5') {
            const outputDiv = document.getElementById('output');
            
            try {
                // Show loading message
                outputDiv.innerHTML = '<div class="loading">Loading data...</div>';
                
                // Fetch data from URL
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const jsonData = await response.json();
                
                // Load the data into HTML
                loadJSONToHTML(jsonData, 'Data from URL');
                
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">Error loading data: ${error.message}</div>`;
            }
        }

        // Function to load JSON data from a string
        function loadJSONFromString() {
            // Sample JSON data
            const jsonString = `[
                {
                    "id": 1,
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "id": 2,
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "role": "Designer"
                },
                {
                    "id": 3,
                    "name": "Bob Johnson",
                    "email": "<EMAIL>",
                    "role": "Manager"
                }
            ]`;
            
            try {
                const jsonData = JSON.parse(jsonString);
                loadJSONToHTML(jsonData, 'Data from String');
            } catch (error) {
                document.getElementById('output').innerHTML = 
                    `<div class="error">Error parsing JSON: ${error.message}</div>`;
            }
        }

        // Main function to load JSON data into HTML
        function loadJSONToHTML(data, title = 'JSON Data') {
            const outputDiv = document.getElementById('output');
            
            // Create HTML content
            let html = `<h2>${title}</h2>`;
            
            if (Array.isArray(data)) {
                // Handle array of objects
                html += `<p>Found ${data.length} items:</p>`;
                
                data.forEach((item, index) => {
                    html += `<div class="data-item">`;
                    html += `<h3>Item ${index + 1}</h3>`;
                    html += formatObject(item);
                    html += `</div>`;
                });
            } else if (typeof data === 'object' && data !== null) {
                // Handle single object
                html += `<div class="data-item">`;
                html += formatObject(data);
                html += `</div>`;
            } else {
                // Handle primitive values
                html += `<div class="data-item">${data}</div>`;
            }
            
            outputDiv.innerHTML = html;
        }

        // Helper function to format object properties
        function formatObject(obj) {
            let html = '';
            
            for (const [key, value] of Object.entries(obj)) {
                html += `<p><strong>${key}:</strong> `;
                
                if (typeof value === 'object' && value !== null) {
                    // Handle nested objects/arrays
                    html += `<pre>${JSON.stringify(value, null, 2)}</pre>`;
                } else {
                    html += `${value}`;
                }
                
                html += `</p>`;
            }
            
            return html;
        }

        // Function to clear the output
        function clearData() {
            document.getElementById('output').innerHTML = '';
        }

        // Alternative: Load JSON from file input
        function loadJSONFromFile(event) {
            const file = event.target.files[0];
            
            if (file) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    try {
                        const jsonData = JSON.parse(e.target.result);
                        loadJSONToHTML(jsonData, `Data from ${file.name}`);
                    } catch (error) {
                        document.getElementById('output').innerHTML = 
                            `<div class="error">Error parsing JSON file: ${error.message}</div>`;
                    }
                };
                
                reader.readAsText(file);
            }
        }

        // Simple function for basic use cases
        function simpleJSONLoader(jsonData, targetElementId) {
            const element = document.getElementById(targetElementId);
            
            if (!element) {
                console.error(`Element with ID '${targetElementId}' not found`);
                return;
            }
            
            // Convert JSON to HTML list
            if (Array.isArray(jsonData)) {
                let html = '<ul>';
                jsonData.forEach(item => {
                    html += `<li>${typeof item === 'object' ? JSON.stringify(item) : item}</li>`;
                });
                html += '</ul>';
                element.innerHTML = html;
            } else {
                element.innerHTML = `<pre>${JSON.stringify(jsonData, null, 2)}</pre>`;
            }
        }
    </script>
</body>
</html>
