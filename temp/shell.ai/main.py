import joblib

# Load the new compatible model
model = joblib.load('fuel_blend_predictor_model_new.pkl')
print("Model loaded successfully!")
print(f"Model type: {type(model)}")
print(f"Model parameters: {model.get_params()}")

# Note: This is a new untrained model. You need to train it with your data:
# model.fit(X_train, y_train)
# Then save it: joblib.dump(model, 'fuel_blend_predictor_model_trained.pkl')
