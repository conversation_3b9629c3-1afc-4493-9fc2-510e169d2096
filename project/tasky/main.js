document.addEventListener('DOMContentLoaded', () => {
    const newTaskInput = document.getElementById('new-task-input');
    const addTaskButton = document.getElementById('add-task-button');
    const taskContainer = document.querySelector('.task-container');

    const addNewTask = () => {
        const taskText = newTaskInput.value.trim();

        if (taskText === '') return; // Stop if input is empty

        const taskId = Date.now(); // Simple unique ID

        taskContainer.innerHTML += `
            <div class="task-item" data-id="${taskId}">
                <input type="checkbox" class="task-checkbox" id="task-${taskId}">
                <input type="text" class="task-text" value="${taskText}" id="task-${taskId}-text" readonly>
                <button class="delete-button"><i class="fas fa-trash"></i></button>
            </div>
        `;

        newTaskInput.value = ''; // Clear input

        // Re-attach event listeners to all tasks after adding new HTML
        attachTaskEventListeners();
    };

    const attachTaskEventListeners = () => {
        // Get all checkboxes, text inputs, and delete buttons
        const checkboxes = document.querySelectorAll('.task-checkbox');
        const textInputs = document.querySelectorAll('.task-text');
        const deleteButtons = document.querySelectorAll('.delete-button');

        checkboxes.forEach(checkbox => {
            checkbox.onchange = () => { // Use onchange for brevity
                checkbox.closest('.task-item').classList.toggle('completed', checkbox.checked);
            };
        });

        textInputs.forEach(textInput => {
            textInput.ondblclick = () => { // Use ondblclick
                textInput.removeAttribute('readonly');
                textInput.focus();
            };
            textInput.onblur = () => { // Use onblur
                textInput.setAttribute('readonly', 'true');
            };
            textInput.onkeypress = (e) => { // Use onkeypress
                if (e.key === 'Enter') {
                    textInput.blur();
                }
            };
        });

        deleteButtons.forEach(button => {
            button.onclick = () => { // Use onclick
                button.closest('.task-item').remove();
            };
        });
    };

    addTaskButton.addEventListener('click', addNewTask);
    newTaskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addNewTask();
        }
    });

    // Initial attachment of listeners in case there are pre-existing tasks (though none in your HTML)
    attachTaskEventListeners();
});